"""
教学内容配置模块，定义教师需要进行的教学内容。
"""
from typing import Dict, Any, List


def get_default_teaching_content() -> Dict[str, Any]:
    """
    获取默认教学内容配置。
    
    Returns:
        默认教学内容配置字典
    """
    return {
        "subject": "数学",
        "topic": "分数乘法",
        "grade_level": "小学五年级",
        "difficulty": "中等",
        "duration": 45,  # 分钟
        "objectives": [
            "理解分数乘法的概念",
            "掌握分数乘法的计算方法",
            "能够解决简单的分数乘法应用题"
        ],
        "key_points": [
            "分数乘以整数：分子乘以整数，分母不变",
            "分数乘以分数：分子相乘作为新分数的分子，分母相乘作为新分数的分母",
            "计算后需要化简分数到最简形式"
        ],
        "examples": [
            "1/2 × 3 = 3/2 = 1又1/2",
            "2/3 × 3/4 = 6/12 = 1/2"
        ],
        "common_misconceptions": [
            "错误地认为分数乘法和加法规则相同",
            "忘记化简最终结果",
            "在分数乘以整数时错误地改变分母"
        ],
        "teaching_strategies": [
            "使用视觉模型展示分数乘法",
            "通过实际生活例子解释概念",
            "提供足够的练习机会",
            "鼓励学生解释自己的思考过程"
        ],
        "materials_needed": [
            "分数教具",
            "练习题",
            "视觉辅助材料"
        ],
        "assessment_methods": [
            "课堂提问",
            "练习题完成情况",
            "小测验"
        ],
        "description": "这节课主要教授小学五年级学生分数乘法的概念和计算方法。教学内容包括分数乘以整数和分数乘以分数两部分，通过视觉模型和实际例子帮助学生理解，并通过练习巩固所学知识。"
    }


def get_teaching_content(content_id: str = None) -> Dict[str, Any]:
    """
    根据ID获取教学内容配置。
    
    Args:
        content_id: 教学内容ID，如果为None则返回默认配置
        
    Returns:
        教学内容配置字典
    """
    # 这里可以根据ID从数据库或文件中加载不同的教学内容
    # 目前仅实现返回默认配置
    return get_default_teaching_content()


def get_all_teaching_contents() -> List[Dict[str, Any]]:
    """
    获取所有可用的教学内容配置。
    
    Returns:
        教学内容配置列表
    """
    # 这里可以从数据库或文件中加载所有教学内容
    # 目前仅返回包含默认配置的列表
    return [get_default_teaching_content()]


def save_teaching_content(content_id: str, content_data: Dict[str, Any]) -> bool:
    """
    保存教学内容配置。
    
    Args:
        content_id: 教学内容ID
        content_data: 教学内容配置数据
        
    Returns:
        保存是否成功
    """
    # 这里可以将配置保存到数据库或文件中
    # 目前仅打印信息
    print(f"保存教学内容配置: {content_id}")
    print(content_data)
    return True
