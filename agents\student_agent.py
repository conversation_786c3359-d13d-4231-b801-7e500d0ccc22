"""
学生模拟代理模块，负责根据状态生成学生行为和对话。
"""
from typing import Dict, List, Any, Optional
import os
import json
import time
import random
from dotenv import load_dotenv
import openai

# 加载环境变量
load_dotenv()

from openai import OpenAI

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"), base_url=os.getenv("API_BASE"))


class StudentAgent:
    """
    学生模拟代理类，负责根据状态生成学生行为和对话。
    """

    def __init__(self, student_profile: Dict[str, Any], observable_behaviors: Dict[str, Any]):
        """
        初始化学生模拟代理。

        Args:
            student_profile: 学生基础信息和初始状态
            observable_behaviors: 学生可观察行为配置
        """
        self.student_profile = student_profile
        self.observable_behaviors = observable_behaviors
        self.personality = student_profile.get("personality", {})
        self.learning_style = student_profile.get("learning_style", {})
        self.problems = student_profile.get("problems", [])

    def generate_initial_performance(self, student_state: Dict[str, Any],
                                    environment_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成初始学生表现。

        Args:
            student_state: 学生初始状态
            environment_state: 环境状态

        Returns:
            包含学生初始表现的字典
        """
        # 构建提示
        prompt = self._build_initial_performance_prompt(student_state, environment_state)

        # 调用LLM生成表现
        performance = self._generate_performance_with_llm(prompt)

        return {
            "behavior": performance.get("behavior", ""),
            "speech": performance.get("speech", ""),
            "description": performance.get("description", ""),
            "timestamp": performance.get("timestamp", 0)
        }

    def generate_performance(self, teacher_action: Dict[str, Any], student_state: Dict[str, Any],
                            environment_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据教师行为生成学生表现。

        Args:
            teacher_action: 教师行为信息
            student_state: 当前学生状态
            environment_state: 当前环境状态

        Returns:
            包含学生表现的字典，可能包含环境变化
        """
        # 构建提示
        prompt = self._build_performance_prompt(teacher_action, student_state, environment_state)

        # 调用LLM生成表现
        performance = self._generate_performance_with_llm(prompt)

        return {
            "behavior": performance.get("behavior", ""),
            "speech": performance.get("speech", ""),
            "description": performance.get("description", ""),
            "environment_change": performance.get("environment_change", None),
            "timestamp": performance.get("timestamp", 0)
        }

    def update_state(self, teacher_action: Dict[str, Any],
                    current_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据教师行为更新学生状态。

        Args:
            teacher_action: 教师行为信息
            current_state: 当前学生状态

        Returns:
            更新后的学生状态
        """
        # 构建提示
        prompt = self._build_state_update_prompt(teacher_action, current_state)

        # 调用LLM更新状态
        new_state = self._update_state_with_llm(prompt, current_state)

        return new_state

    def _build_initial_performance_prompt(self, student_state: Dict[str, Any],
                                         environment_state: Dict[str, Any]) -> str:
        """
        构建生成初始表现的提示。

        Args:
            student_state: 学生初始状态
            environment_state: 环境状态

        Returns:
            提示字符串
        """
        prompt = f"""
        你是一个模拟学生的AI，需要根据学生的基本信息和状态生成初始表现。

        ## 学生基本信息
        姓名: {self.student_profile.get('name', '未知')}
        年龄: {self.student_profile.get('age', '未知')}
        性别: {self.student_profile.get('gender', '未知')}
        年级: {self.student_profile.get('grade', '未知')}

        ## 学生性格特点
        {json.dumps(self.personality, ensure_ascii=False, indent=2)}

        ## 学生学习风格
        {json.dumps(self.learning_style, ensure_ascii=False, indent=2)}

        ## 学生问题
        {json.dumps(self.problems, ensure_ascii=False, indent=2)}

        ## 当前学生状态
        {json.dumps(student_state, ensure_ascii=False, indent=2)}

        ## 环境状态
        {json.dumps(environment_state, ensure_ascii=False, indent=2)}

        ## 可观察行为
        {json.dumps(self.observable_behaviors, ensure_ascii=False, indent=2)}

        请根据以上信息，生成学生的初始表现，包括行为、语言和整体描述。
        返回格式为JSON，包含behavior（行为）、speech（语言）和description（整体描述）字段。
        """

        return prompt

    def _build_performance_prompt(self, teacher_action: Dict[str, Any],
                                 student_state: Dict[str, Any],
                                 environment_state: Dict[str, Any]) -> str:
        """
        构建生成表现的提示。

        Args:
            teacher_action: 教师行为信息
            student_state: 当前学生状态
            environment_state: 当前环境状态

        Returns:
            提示字符串
        """
        prompt = f"""
        你是一个模拟学生的AI，需要根据教师的行为和语言生成学生的表现。

        ## 学生基本信息
        姓名: {self.student_profile.get('name', '未知')}
        年龄: {self.student_profile.get('age', '未知')}
        性别: {self.student_profile.get('gender', '未知')}
        年级: {self.student_profile.get('grade', '未知')}

        ## 学生性格特点
        {json.dumps(self.personality, ensure_ascii=False, indent=2)}

        ## 学生学习风格
        {json.dumps(self.learning_style, ensure_ascii=False, indent=2)}

        ## 学生问题
        {json.dumps(self.problems, ensure_ascii=False, indent=2)}

        ## 当前学生状态
        {json.dumps(student_state, ensure_ascii=False, indent=2)}

        ## 环境状态
        {json.dumps(environment_state, ensure_ascii=False, indent=2)}

        ## 教师行为
        行为: {teacher_action.get('action', '')}
        语言: {teacher_action.get('speech', '')}

        ## 可观察行为
        {json.dumps(self.observable_behaviors, ensure_ascii=False, indent=2)}

        请根据以上信息，特别是教师的行为和语言，生成学生的表现，包括行为、语言和整体描述。
        同时，如果教师的行为可能导致环境发生变化（如打开窗户、调整灯光、移动座位等），请描述环境的变化。

        返回格式为JSON，包含以下字段：
        - behavior（行为）：学生的行为表现
        - speech（语言）：学生说的话
        - description（整体描述）：学生整体表现的描述
        - environment_change（环境变化，可选）：如果环境因教师或学生行为发生变化，请描述变化后的环境
        """

        return prompt

    def _build_state_update_prompt(self, teacher_action: Dict[str, Any],
                                  current_state: Dict[str, Any]) -> str:
        """
        构建更新状态的提示。

        Args:
            teacher_action: 教师行为信息
            current_state: 当前学生状态

        Returns:
            提示字符串
        """
        prompt = f"""
        你是一个模拟学生的AI，需要根据教师的行为和语言更新学生的状态。

        ## 学生基本信息
        姓名: {self.student_profile.get('name', '未知')}
        年龄: {self.student_profile.get('age', '未知')}
        性别: {self.student_profile.get('gender', '未知')}
        年级: {self.student_profile.get('grade', '未知')}

        ## 学生性格特点
        {json.dumps(self.personality, ensure_ascii=False, indent=2)}

        ## 学生学习风格
        {json.dumps(self.learning_style, ensure_ascii=False, indent=2)}

        ## 学生问题
        {json.dumps(self.problems, ensure_ascii=False, indent=2)}

        ## 当前学生状态
        {json.dumps(current_state, ensure_ascii=False, indent=2)}

        ## 教师行为
        行为: {teacher_action.get('action', '')}
        语言: {teacher_action.get('speech', '')}

        请根据以上信息，特别是教师的行为和语言，更新学生的状态。
        返回格式为JSON，包含与当前状态相同的字段，但值可能有变化。
        特别注意：
        1. 如果教师的行为和语言有效解决了学生的问题，可以将problem_solved设为true
        2. 情绪、注意力、参与度等状态应该根据教师的行为和语言相应变化
        3. 状态变化应该符合学生的性格特点和学习风格
        """

        return prompt

    def _generate_performance_with_llm(self, prompt: str) -> Dict[str, Any]:
        """
        使用LLM生成学生表现。

        Args:
            prompt: 提示字符串

        Returns:
            包含学生表现的字典
        """
        try:
            # 调用OpenAI API
            response = client.chat.completions.create(
                # model="gpt-4.1",
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "你是一个模拟学生的AI助手，根据提供的信息生成学生的表现。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                # max_tokens=500,
                response_format={"type": "json_object"}
            )

            # 解析响应
            content = response.choices[0].message.content

            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)

            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content

            try:
                performance = json.loads(json_str)
            except json.JSONDecodeError:
                # 如果无法解析JSON，则手动构建结果
                print(f"生成学生表现时出错，无法解析JSON: {json_str}")
                performance = {
                    "behavior": "学生坐在座位上，看起来有些困惑。",
                    "speech": "我不太明白这个问题...",
                    "description": "学生表现出困惑和不确定的状态。",
                    "timestamp": time.time()
                }

            return performance

        except Exception as e:
            print(f"生成学生表现时出错: {e}")
            # 返回默认表现
            return {
                "behavior": "学生坐在座位上，看起来有些困惑。",
                "speech": "我不太明白这个问题...",
                "description": "学生表现出困惑和不确定的状态。",
                "timestamp": time.time()
            }

    def _update_state_with_llm(self, prompt: str, current_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用LLM更新学生状态。

        Args:
            prompt: 提示字符串
            current_state: 当前学生状态

        Returns:
            更新后的学生状态
        """
        try:
            # 调用OpenAI API
            response = client.chat.completions.create(
                # model="gpt-4.1",
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "你是一个模拟学生的AI助手，根据提供的信息更新学生的状态。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                # max_tokens=500,
                response_format={"type": "json_object"}
            )
            # 解析响应
            content = response.choices[0].message.content

            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)

            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content

            try:
                new_state = json.loads(json_str)
                # 确保新状态包含所有必要的字段
                for key in current_state:
                    if key not in new_state:
                        new_state[key] = current_state[key]
            except json.JSONDecodeError:
                # 如果无法解析JSON，则返回当前状态并稍作修改
                print(f"更新学生状态时出错，无法解析JSON: {json_str}")
                new_state = current_state.copy()
                # 随机修改一些状态值
                for key in ["attention", "emotion", "engagement"]:
                    if key in new_state and isinstance(new_state[key], (int, float)):
                        new_state[key] = max(0, min(100, new_state[key] + random.randint(-10, 10)))

            return new_state

        except Exception as e:
            print(f"更新学生状态时出错: {e}")
            # 返回当前状态
            return current_state
