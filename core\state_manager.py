"""
状态管理器模块，负责管理学生状态和环境状态。
"""
from typing import Dict, List, Any, Optional
import json
import os


class StateManager:
    """
    状态管理器类，负责管理和更新学生状态和环境状态。
    """
    
    def __init__(self, student_profile: Dict[str, Any], environment_config: Optional[Dict[str, Any]] = None):
        """
        初始化状态管理器。
        
        Args:
            student_profile: 学生基础信息和初始状态
            environment_config: 环境配置信息（可选）
        """
        self.student_profile = student_profile
        self.student_state = student_profile.get("initial_state", {})
        self.environment_state = environment_config or {"description": "普通教室环境，安静，光线充足。"}
        self.interaction_history = []
        self.current_round = 0
        
    def update_student_state(self, teacher_action: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据教师行为更新学生状态。
        
        Args:
            teacher_action: 教师行为信息，包括语言和动作
            
        Returns:
            更新后的学生状态
        """
        # 这里的逻辑将由学生代理实现，状态管理器只负责存储状态
        # 此处仅作为接口定义
        return self.student_state
    
    def update_environment_state(self, new_environment: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新环境状态。
        
        Args:
            new_environment: 新的环境状态信息
            
        Returns:
            更新后的环境状态
        """
        self.environment_state.update(new_environment)
        return self.environment_state
    
    def record_interaction(self, teacher_action: Dict[str, Any], student_performance: Dict[str, Any], 
                          expert_feedback: Optional[Dict[str, Any]] = None) -> None:
        """
        记录一轮交互信息。
        
        Args:
            teacher_action: 教师行为信息
            student_performance: 学生表现信息
            expert_feedback: 专家反馈信息（可选）
        """
        self.current_round += 1
        interaction = {
            "round": self.current_round,
            "teacher_action": teacher_action,
            "student_performance": student_performance,
            "student_state": self.student_state.copy(),
            "environment_state": self.environment_state.copy()
        }
        
        if expert_feedback:
            interaction["expert_feedback"] = expert_feedback
            
        self.interaction_history.append(interaction)
    
    def get_current_state(self) -> Dict[str, Any]:
        """
        获取当前状态信息。
        
        Returns:
            包含当前学生状态和环境状态的字典
        """
        return {
            "student_state": self.student_state,
            "environment_state": self.environment_state,
            "current_round": self.current_round
        }
    
    def get_interaction_history(self) -> List[Dict[str, Any]]:
        """
        获取交互历史记录。
        
        Returns:
            交互历史记录列表
        """
        return self.interaction_history
    
    def save_state(self, file_path: str) -> None:
        """
        将当前状态保存到文件。
        
        Args:
            file_path: 保存文件路径
        """
        state_data = {
            "student_profile": self.student_profile,
            "student_state": self.student_state,
            "environment_state": self.environment_state,
            "interaction_history": self.interaction_history,
            "current_round": self.current_round
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, ensure_ascii=False, indent=2)
    
    def load_state(self, file_path: str) -> None:
        """
        从文件加载状态。
        
        Args:
            file_path: 状态文件路径
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"状态文件 {file_path} 不存在")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        self.student_profile = state_data.get("student_profile", {})
        self.student_state = state_data.get("student_state", {})
        self.environment_state = state_data.get("environment_state", {})
        self.interaction_history = state_data.get("interaction_history", [])
        self.current_round = state_data.get("current_round", 0)
