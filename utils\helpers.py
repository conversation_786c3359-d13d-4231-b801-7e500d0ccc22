"""
辅助函数模块，提供各种工具函数。
"""
import os
import json
import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime


def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    加载JSON文件。
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        JSON数据字典
    
    Raises:
        FileNotFoundError: 文件不存在
        json.JSONDecodeError: JSON解析错误
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def save_json_file(file_path: str, data: Dict[str, Any]) -> None:
    """
    保存数据到JSON文件。
    
    Args:
        file_path: JSON文件路径
        data: 要保存的数据
    """
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def parse_teacher_input(input_text: str) -> Dict[str, str]:
    """
    解析教师输入，提取行为和语言。
    
    Args:
        input_text: 教师输入的文本
        
    Returns:
        包含行为和语言的字典
    """
    # 使用正则表达式提取括号内的行为
    actions = re.findall(r'\((.*?)\)', input_text)
    action_text = ' '.join(actions) if actions else ""
    
    # 移除括号内容，剩余部分为语言
    speech = re.sub(r'\(.*?\)', '', input_text).strip()
    
    return {
        "action": action_text,
        "speech": speech,
        "raw_input": input_text
    }


def format_timestamp(timestamp: Optional[float] = None) -> str:
    """
    格式化时间戳为可读字符串。
    
    Args:
        timestamp: 时间戳，如果为None则使用当前时间
        
    Returns:
        格式化后的时间字符串
    """
    if timestamp is None:
        timestamp = datetime.now().timestamp()
    
    dt = datetime.fromtimestamp(timestamp)
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def normalize_score(value: Union[int, float], min_val: int = 0, max_val: int = 100) -> int:
    """
    将值标准化到指定范围内。
    
    Args:
        value: 原始值
        min_val: 最小值
        max_val: 最大值
        
    Returns:
        标准化后的值
    """
    return max(min_val, min(max_val, int(value)))


def calculate_change(old_value: Union[int, float], new_value: Union[int, float]) -> str:
    """
    计算并格式化值的变化。
    
    Args:
        old_value: 旧值
        new_value: 新值
        
    Returns:
        格式化后的变化字符串
    """
    change = new_value - old_value
    if change > 0:
        return f"+{change}"
    return str(change)


def create_session_id() -> str:
    """
    创建唯一的会话ID。
    
    Returns:
        会话ID字符串
    """
    import uuid
    return str(uuid.uuid4())


def ensure_directory(directory: str) -> None:
    """
    确保目录存在，如果不存在则创建。
    
    Args:
        directory: 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不允许的字符。
    
    Args:
        filename: 原始文件名
        
    Returns:
        清理后的文件名
    """
    # 移除不允许的字符
    return re.sub(r'[\\/*?:"<>|]', "", filename)
