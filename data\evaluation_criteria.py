"""
教师评价标准配置模块，定义教师评价标准和反馈内容。
"""
from typing import Dict, Any, List


def get_default_evaluation_criteria() -> Dict[str, Any]:
    """
    获取默认教师评价标准配置。
    
    Returns:
        默认教师评价标准配置字典
    """
    return {
        "teaching_skills": {
            "explanation_clarity": {
                "description": "教师解释概念的清晰度和有效性",
                "criteria": [
                    "使用简明易懂的语言",
                    "提供适当的例子和类比",
                    "分步骤讲解复杂概念",
                    "检查学生理解程度并调整解释"
                ],
                "weight": 20  # 权重百分比
            },
            "questioning_technique": {
                "description": "教师提问的质量和有效性",
                "criteria": [
                    "提问难度适中，符合学生水平",
                    "问题设计能促进思考和讨论",
                    "给予学生足够的思考时间",
                    "根据学生回答提供适当的反馈"
                ],
                "weight": 15
            },
            "feedback_quality": {
                "description": "教师反馈的质量和有效性",
                "criteria": [
                    "提供具体而非笼统的反馈",
                    "平衡正面反馈和改进建议",
                    "及时给予反馈",
                    "反馈针对行为而非人格"
                ],
                "weight": 15
            }
        },
        "student_engagement": {
            "motivation_strategies": {
                "description": "教师激发学生学习动机的策略",
                "criteria": [
                    "创造有趣和相关的学习体验",
                    "认可和赞赏学生的努力和进步",
                    "帮助学生建立自信心",
                    "展示学习内容的价值和应用"
                ],
                "weight": 10
            },
            "participation_encouragement": {
                "description": "教师鼓励学生参与的方法",
                "criteria": [
                    "创造安全的课堂氛围",
                    "使用多种参与方式（口头、书面、小组等）",
                    "关注并调动不活跃的学生",
                    "对参与给予积极回应"
                ],
                "weight": 10
            }
        },
        "problem_student_handling": {
            "understanding_issues": {
                "description": "教师理解学生问题的深度",
                "criteria": [
                    "识别学生行为背后的原因",
                    "考虑学生的个人情况和背景",
                    "区分暂时性问题和长期性问题",
                    "寻找问题的模式和触发因素"
                ],
                "weight": 10
            },
            "intervention_strategies": {
                "description": "教师干预问题的策略",
                "criteria": [
                    "使用适当的干预方法",
                    "保持冷静和专业",
                    "避免在全班面前使学生难堪",
                    "在必要时寻求其他资源的帮助"
                ],
                "weight": 10
            },
            "relationship_building": {
                "description": "教师与问题学生建立关系的能力",
                "criteria": [
                    "展示真诚的关心和尊重",
                    "建立信任和安全感",
                    "了解学生的兴趣和优势",
                    "保持积极的期望"
                ],
                "weight": 10
            }
        },
        "feedback_focus": {
            "strengths_identification": {
                "description": "识别教师表现中的优点",
                "guidance": "关注教师有效的教学策略、积极的师生互动、成功的干预方法等"
            },
            "areas_for_improvement": {
                "description": "识别教师需要改进的方面",
                "guidance": "关注可能的误解、错失的机会、不当的反应、需要加强的技能等"
            },
            "actionable_suggestions": {
                "description": "提供可行的改进建议",
                "guidance": "提供具体、实用的建议，包括替代策略、资源推荐、专业发展方向等"
            }
        },
        "report_structure": {
            "immediate_feedback": {
                "description": "每轮交互后的即时反馈",
                "sections": [
                    "教师行为的优点",
                    "教师行为的不足",
                    "改进建议",
                    "对学生状态的分析",
                    "对下一步教学的建议"
                ]
            },
            "final_report": {
                "description": "整个教学过程的最终评估报告",
                "sections": [
                    "总体评价",
                    "主要优点",
                    "主要不足",
                    "具体的改进建议",
                    "问题处理能力评估",
                    "未来教学建议",
                    "总结"
                ]
            }
        }
    }


def get_evaluation_criteria(criteria_id: str = None) -> Dict[str, Any]:
    """
    根据ID获取教师评价标准配置。
    
    Args:
        criteria_id: 评价标准配置ID，如果为None则返回默认配置
        
    Returns:
        评价标准配置字典
    """
    # 这里可以根据ID从数据库或文件中加载不同的评价标准配置
    # 目前仅实现返回默认配置
    return get_default_evaluation_criteria()


def get_all_evaluation_criteria() -> List[Dict[str, Any]]:
    """
    获取所有可用的评价标准配置。
    
    Returns:
        评价标准配置列表
    """
    # 这里可以从数据库或文件中加载所有评价标准配置
    # 目前仅返回包含默认配置的列表
    return [get_default_evaluation_criteria()]


def save_evaluation_criteria(criteria_id: str, criteria_data: Dict[str, Any]) -> bool:
    """
    保存评价标准配置。
    
    Args:
        criteria_id: 评价标准配置ID
        criteria_data: 评价标准配置数据
        
    Returns:
        保存是否成功
    """
    # 这里可以将配置保存到数据库或文件中
    # 目前仅打印信息
    print(f"保存评价标准配置: {criteria_id}")
    print(criteria_data)
    return True
