# TutorHelper - 基于学生状态变化的与真人教师对话交互反馈系统

## 项目描述

TutorHelper是一个训练真人教师对学生的交互和教育能力的系统。用户（真人教师）可以在模拟的一对一课堂环境中，与由AI模拟的问题学生进行交互，训练自己教导问题学生的能力。

系统通过两个AI代理实现：

1. 学生模拟代理 - 根据自身状态和教师的教导方式做出反馈
2. 教育专家代理 - 评估教师的表现并提供反馈

## 功能特点

- 模拟真实的师生互动场景
- 实时反馈学生状态变化
- 教育专家提供专业指导和建议
- 生成详细的教学过程评估报告
- 支持命令行和Web界面两种交互方式

## 使用方法

### 安装依赖

```
pip install -r requirements.txt
```

### 运行Web界面

```
python main.py --ui web
```

### 运行命令行界面

```
python main.py --ui cli
```

### 自定义配置

系统支持以下五个方面的自定义配置：

1. **教学内容** ：在data/teaching_content.py中定义
2. **学生基础信息和初始状态** ：在data/student_profiles.py中定义
3. **学生状态的自然语言描述** ：在学生配置和可观察行为中定义
4. **学生可观察行为** ：在data/observable_behaviors.py中定义
5. **教师评价标准和反馈内容** ：在data/evaluation_critera.py中定义

## 项目结构

```
TutorHelper/
├── agents/         # AI代理实现
├── data/           # 配置数据
├── ui/             # 用户界面
├── core/           # 核心逻辑
├── utils/          # 工具函数
└── main.py         # 程序入口
```
