"""
主程序入口模块，处理命令行参数，启动相应的界面。
"""
import os
import argparse
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """
    主函数，解析命令行参数并启动相应的界面。
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='教师-学生交互模拟系统')
    parser.add_argument('--ui', type=str, choices=['cli', 'web'], default='web',
                      help='界面类型：cli（命令行）或web（网页）')
    parser.add_argument('--share', action='store_true',
                      help='是否公开分享Web界面（仅在ui=web时有效）')
    parser.add_argument('--debug', action='store_true',
                      help='是否启用调试模式')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")
    
    # 检查OpenAI API密钥
    if not os.getenv("OPENAI_API_KEY"):
        logger.warning("未设置OPENAI_API_KEY环境变量，请在.env文件中设置或直接设置环境变量")
        print("警告：未设置OPENAI_API_KEY环境变量")
        print("请在项目根目录创建.env文件并添加以下内容：")
        print("OPENAI_API_KEY=your_api_key_here")
        print("或者直接设置环境变量")
        
        # 询问是否继续
        response = input("是否继续运行（将使用模拟响应）？(y/n): ")
        if response.lower() != 'y':
            return
    
    # 启动相应的界面
    if args.ui == 'cli':
        logger.info("启动命令行界面")
        from ui.cli_ui import run
        run()
    else:  # web
        logger.info("启动Web界面")
        from ui.web_ui import run
        run(share=args.share)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.exception(f"程序运行出错: {e}")
