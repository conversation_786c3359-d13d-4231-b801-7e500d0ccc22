"""
学生基础信息和状态配置模块，定义学生的基本信息和初始状态。
"""
from typing import Dict, Any, List


def get_default_student_profile() -> Dict[str, Any]:
    """
    获取默认学生配置。
    
    Returns:
        默认学生配置字典
    """
    return {
        "name": "小明",
        "age": 11,
        "gender": "男",
        "grade": "五年级",
        "personality": {
            "introversion": 70,  # 0-100，越高越内向
            "sensitivity": 80,   # 0-100，越高越敏感
            "anxiety": 65,       # 0-100，越高越焦虑
            "stubbornness": 75,  # 0-100，越高越固执
            "curiosity": 40,     # 0-100，越高越好奇
            "traits": [
                "内向害羞，不善于表达",
                "对批评很敏感，容易受伤",
                "有时固执己见，不愿接受新观点",
                "在熟悉的环境中会逐渐放松"
            ]
        },
        "learning_style": {
            "visual": 70,        # 0-100，视觉学习偏好
            "auditory": 40,      # 0-100，听觉学习偏好
            "kinesthetic": 50,   # 0-100，动觉学习偏好
            "preferences": [
                "喜欢通过图表和图像学习",
                "需要安静的环境",
                "喜欢独自思考问题",
                "需要足够的时间处理信息"
            ],
            "strengths": [
                "数学逻辑思维较强",
                "记忆力不错",
                "专注力较好"
            ],
            "weaknesses": [
                "语言表达能力较弱",
                "小组合作能力不足",
                "不善于提问"
            ]
        },
        "problems": [
            {
                "type": "学习困难",
                "description": "对分数概念理解困难，特别是在分数乘法方面",
                "severity": 70,  # 0-100，越高越严重
                "triggers": [
                    "被要求在全班面前回答问题",
                    "遇到复杂的分数乘法问题",
                    "感到时间压力"
                ],
                "symptoms": [
                    "回避眼神接触",
                    "坐立不安",
                    "拒绝回答问题",
                    "情绪低落"
                ]
            },
            {
                "type": "社交焦虑",
                "description": "在课堂上不敢发言，害怕被同学嘲笑",
                "severity": 65,
                "triggers": [
                    "需要在全班面前发言",
                    "被点名回答问题",
                    "小组活动中需要表达意见"
                ],
                "symptoms": [
                    "脸红",
                    "声音颤抖",
                    "回避眼神接触",
                    "说话结巴"
                ]
            }
        ],
        "initial_state": {
            "attention": 50,     # 0-100，注意力水平
            "emotion": 40,       # 0-100，情绪状态（越高越积极）
            "engagement": 30,    # 0-100，参与度
            "understanding": 20, # 0-100，对当前主题的理解度
            "frustration": 60,   # 0-100，挫折感
            "trust": 40,         # 0-100，对教师的信任度
            "problem_solved": False,  # 问题是否已解决
            "notes": "学生今天看起来比平时更加紧张和不安，可能是因为即将进行的分数测验"
        },
        "background": {
            "family": "双亲家庭，父母都很忙，较少有时间辅导功课",
            "previous_performance": "数学成绩一般，语文较差，英语中等",
            "interests": ["电子游戏", "画画", "看科幻小说"],
            "significant_events": ["去年因数学成绩不好被父母批评", "曾在班级比赛中因紧张表现不佳"]
        }
    }


def get_student_profile(profile_id: str = None) -> Dict[str, Any]:
    """
    根据ID获取学生配置。
    
    Args:
        profile_id: 学生配置ID，如果为None则返回默认配置
        
    Returns:
        学生配置字典
    """
    # 这里可以根据ID从数据库或文件中加载不同的学生配置
    # 目前仅实现返回默认配置
    return get_default_student_profile()


def get_all_student_profiles() -> List[Dict[str, Any]]:
    """
    获取所有可用的学生配置。
    
    Returns:
        学生配置列表
    """
    # 这里可以从数据库或文件中加载所有学生配置
    # 目前仅返回包含默认配置的列表
    return [get_default_student_profile()]


def save_student_profile(profile_id: str, profile_data: Dict[str, Any]) -> bool:
    """
    保存学生配置。
    
    Args:
        profile_id: 学生配置ID
        profile_data: 学生配置数据
        
    Returns:
        保存是否成功
    """
    # 这里可以将配置保存到数据库或文件中
    # 目前仅打印信息
    print(f"保存学生配置: {profile_id}")
    print(profile_data)
    return True
