"""
命令行界面模块，实现基于命令行的用户交互界面。
"""
from typing import Dict, Any, Optional, Callable
import os
import json
import time
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown
from rich.layout import Layout
from rich.live import Live
from rich import box

from core.simulation import Simulation
from data import teaching_content, student_profiles, observable_behaviors, evaluation_criteria


class CommandLineUI:
    """
    命令行界面类，实现基于命令行的用户交互界面。
    """
    
    def __init__(self):
        """
        初始化命令行界面。
        """
        self.console = Console()
        self.simulation = None
        self.current_state = {}
        
    def start(self):
        """
        启动命令行界面。
        """
        self._clear_screen()
        self._print_welcome()
        
        # 加载配置
        teaching_content_data = teaching_content.get_default_teaching_content()
        student_profile_data = student_profiles.get_default_student_profile()
        observable_behaviors_data = observable_behaviors.get_default_observable_behaviors()
        evaluation_criteria_data = evaluation_criteria.get_default_evaluation_criteria()
        
        # 初始化模拟
        self.simulation = Simulation(
            student_profile=student_profile_data,
            teaching_content=teaching_content_data,
            observable_behaviors=observable_behaviors_data,
            evaluation_criteria=evaluation_criteria_data,
            max_rounds=10,
            callback=self._update_state_callback
        )
        
        # 开始模拟
        initial_state = self.simulation.start()
        self.current_state = initial_state
        
        # 主交互循环
        self._interaction_loop()
    
    def _interaction_loop(self):
        """
        主交互循环。
        """
        while True:
            self._clear_screen()
            self._display_current_state()
            
            # 检查是否已完成
            if self.current_state.get("is_completed", False):
                self._display_final_report()
                break
            
            # 获取教师输入
            teacher_input = self._get_teacher_input()
            
            # 如果输入为exit，则退出
            if teacher_input.lower() == "exit":
                self.console.print("[bold red]退出模拟[/bold red]")
                break
            
            # 处理教师输入
            new_state = self.simulation.process_teacher_input(teacher_input)
            self.current_state = new_state
            
            # 短暂暂停，让用户有时间阅读
            time.sleep(1)
    
    def _update_state_callback(self, new_state: Dict[str, Any]):
        """
        状态更新回调函数。
        
        Args:
            new_state: 新的状态信息
        """
        self.current_state.update(new_state)
    
    def _display_current_state(self):
        """
        显示当前状态。
        """
        # 创建布局
        layout = Layout()
        layout.split_column(
            Layout(name="header"),
            Layout(name="body"),
            Layout(name="footer")
        )
        
        layout["body"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        layout["left"].split_column(
            Layout(name="environment"),
            Layout(name="student")
        )
        
        layout["right"].split_column(
            Layout(name="state"),
            Layout(name="expert")
        )
        
        # 设置内容
        # 标题
        round_info = f"轮次: {self.current_state.get('current_round', 0)}/{self.current_state.get('max_rounds', 10)}"
        layout["header"].update(
            Panel(
                f"[bold blue]教师-学生交互模拟系统[/bold blue]\n{round_info}",
                border_style="blue"
            )
        )
        
        # 环境描述
        environment_description = self.current_state.get("environment_description", "普通教室环境")
        layout["environment"].update(
            Panel(
                f"[bold]环境描述:[/bold]\n{environment_description}",
                title="环境",
                border_style="green"
            )
        )
        
        # 学生可观察信息
        student_performance = self.current_state.get("student_performance", {})
        student_behavior = student_performance.get("behavior", "")
        student_speech = student_performance.get("speech", "")
        student_description = student_performance.get("description", "")
        
        student_info = f"[bold]行为:[/bold] {student_behavior}\n\n"
        student_info += f"[bold]语言:[/bold] \"{student_speech}\"\n\n"
        student_info += f"[bold]描述:[/bold] {student_description}"
        
        layout["student"].update(
            Panel(
                student_info,
                title="学生可观察信息",
                border_style="yellow"
            )
        )
        
        # 学生状态（仅用于调试）
        student_state = self.current_state.get("student_state", {})
        state_table = Table(show_header=True, header_style="bold magenta", box=box.SIMPLE)
        state_table.add_column("状态")
        state_table.add_column("值")
        
        for key, value in student_state.items():
            if isinstance(value, (int, float)) and key not in ["timestamp"]:
                state_table.add_row(key, f"{value}")
        
        layout["state"].update(
            Panel(
                state_table,
                title="学生状态（仅用于调试）",
                border_style="magenta"
            )
        )
        
        # 专家建议
        expert_feedback = self.current_state.get("expert_feedback", {})
        if expert_feedback:
            strengths = expert_feedback.get("strengths", [])
            weaknesses = expert_feedback.get("weaknesses", [])
            suggestions = expert_feedback.get("suggestions", [])
            student_analysis = expert_feedback.get("student_analysis", "")
            next_steps = expert_feedback.get("next_steps", "")
            overall_comment = expert_feedback.get("overall_comment", "")
            
            feedback_text = f"[bold green]优点:[/bold green]\n"
            for strength in strengths:
                feedback_text += f"• {strength}\n"
            
            feedback_text += f"\n[bold red]不足:[/bold red]\n"
            for weakness in weaknesses:
                feedback_text += f"• {weakness}\n"
            
            feedback_text += f"\n[bold yellow]建议:[/bold yellow]\n"
            for suggestion in suggestions:
                feedback_text += f"• {suggestion}\n"
            
            feedback_text += f"\n[bold blue]学生分析:[/bold blue]\n{student_analysis}\n"
            feedback_text += f"\n[bold cyan]下一步:[/bold cyan]\n{next_steps}\n"
            feedback_text += f"\n[bold]总体评价:[/bold]\n{overall_comment}"
        else:
            feedback_text = "尚无专家反馈"
        
        layout["expert"].update(
            Panel(
                feedback_text,
                title="专家建议",
                border_style="cyan"
            )
        )
        
        # 教师输入提示
        teaching_content_desc = self.current_state.get("teaching_content", "")
        footer_text = f"[bold]教学内容:[/bold] {teaching_content_desc}\n\n"
        footer_text += "[bold]请输入您的行为和语言（输入'exit'退出）:[/bold]"
        
        layout["footer"].update(
            Panel(
                footer_text,
                border_style="blue"
            )
        )
        
        # 显示布局
        self.console.print(layout)
    
    def _display_final_report(self):
        """
        显示最终报告。
        """
        final_report = self.current_state.get("final_report", {})
        if not final_report:
            self.console.print("[bold red]无法获取最终报告[/bold red]")
            return
        
        self.console.print("\n")
        self.console.print(Panel("[bold blue]教学过程最终评估报告[/bold blue]", border_style="blue"))
        
        # 总体评价
        overall_evaluation = final_report.get("overall_evaluation", "")
        self.console.print(Panel(f"[bold]总体评价:[/bold]\n{overall_evaluation}", border_style="green"))
        
        # 主要优点
        strengths = final_report.get("strengths", [])
        strengths_text = ""
        for strength in strengths:
            strengths_text += f"• {strength}\n"
        self.console.print(Panel(f"[bold green]主要优点:[/bold green]\n{strengths_text}", border_style="green"))
        
        # 主要不足
        weaknesses = final_report.get("weaknesses", [])
        weaknesses_text = ""
        for weakness in weaknesses:
            weaknesses_text += f"• {weakness}\n"
        self.console.print(Panel(f"[bold red]主要不足:[/bold red]\n{weaknesses_text}", border_style="red"))
        
        # 改进建议
        suggestions = final_report.get("improvement_suggestions", [])
        suggestions_text = ""
        for suggestion in suggestions:
            suggestions_text += f"• {suggestion}\n"
        self.console.print(Panel(f"[bold yellow]改进建议:[/bold yellow]\n{suggestions_text}", border_style="yellow"))
        
        # 问题处理能力评估
        problem_handling = final_report.get("problem_handling_assessment", "")
        self.console.print(Panel(f"[bold]问题处理能力评估:[/bold]\n{problem_handling}", border_style="magenta"))
        
        # 未来教学建议
        future_suggestions = final_report.get("future_teaching_suggestions", "")
        self.console.print(Panel(f"[bold]未来教学建议:[/bold]\n{future_suggestions}", border_style="cyan"))
        
        # 总结
        summary = final_report.get("summary", "")
        self.console.print(Panel(f"[bold]总结:[/bold]\n{summary}", border_style="blue"))
        
        self.console.print("\n按Enter键退出...")
        input()
    
    def _get_teacher_input(self) -> str:
        """
        获取教师输入。
        
        Returns:
            教师输入的字符串
        """
        return input("\n> ")
    
    def _print_welcome(self):
        """
        打印欢迎信息。
        """
        welcome_text = """
        # 欢迎使用教师-学生交互模拟系统
        
        本系统旨在帮助教师训练处理问题学生的能力。
        
        ## 使用说明
        
        1. 系统会模拟一个学生，您将作为教师与其互动
        2. 每轮交互中，您可以输入自己的行为和语言
        3. 系统会根据您的输入生成学生的反应
        4. 教育专家会对您的表现提供反馈
        5. 交互结束后，系统会生成一份评估报告
        
        ## 输入格式
        
        - 使用括号表示行为，例如：(走到学生旁边) 你能告诉我你遇到了什么困难吗？
        - 输入'exit'可以随时退出模拟
        
        准备好开始了吗？按Enter键继续...
        """
        
        self.console.print(Markdown(welcome_text))
        input()
    
    def _clear_screen(self):
        """
        清屏。
        """
        os.system('cls' if os.name == 'nt' else 'clear')


def run():
    """
    运行命令行界面。
    """
    ui = CommandLineUI()
    ui.start()
