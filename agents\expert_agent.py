"""
教育专家代理模块，负责评估教师表现并提供反馈。
"""
from typing import Dict, List, Any, Optional
import os
import json
import time
from dotenv import load_dotenv
import openai

# 加载环境变量
load_dotenv()

from openai import OpenAI

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"), base_url=os.getenv("API_BASE"))


class ExpertAgent:
    """
    教育专家代理类，负责评估教师表现并提供反馈。
    """
    
    def __init__(self, evaluation_criteria: Dict[str, Any], teaching_content: Dict[str, Any]):
        """
        初始化教育专家代理。
        
        Args:
            evaluation_criteria: 教师评价标准配置
            teaching_content: 教学内容配置
        """
        self.evaluation_criteria = evaluation_criteria
        self.teaching_content = teaching_content
        self.feedback_history = []
        
    def generate_feedback(self, teacher_action: Dict[str, Any], 
                         student_performance: Dict[str, Any],
                         student_state: Dict[str, Any],
                         environment_state: Dict[str, Any],
                         current_round: int) -> Dict[str, Any]:
        """
        生成对教师表现的反馈。
        
        Args:
            teacher_action: 教师行为信息
            student_performance: 学生表现信息
            student_state: 当前学生状态
            environment_state: 当前环境状态
            current_round: 当前交互轮数
            
        Returns:
            包含反馈信息的字典
        """
        # 构建提示
        prompt = self._build_feedback_prompt(
            teacher_action, 
            student_performance, 
            student_state, 
            environment_state,
            current_round
        )
        
        # 调用LLM生成反馈
        feedback = self._generate_feedback_with_llm(prompt)
        
        # 记录反馈
        self.feedback_history.append({
            "round": current_round,
            "teacher_action": teacher_action,
            "student_performance": student_performance,
            "feedback": feedback,
            "timestamp": time.time()
        })
        
        return feedback
    
    def generate_final_report(self, interaction_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成最终评估报告。
        
        Args:
            interaction_history: 交互历史记录
            
        Returns:
            包含最终评估报告的字典
        """
        # 构建提示
        prompt = self._build_final_report_prompt(interaction_history)
        
        # 调用LLM生成最终报告
        final_report = self._generate_final_report_with_llm(prompt)
        
        return final_report
    
    def _build_feedback_prompt(self, teacher_action: Dict[str, Any],
                              student_performance: Dict[str, Any],
                              student_state: Dict[str, Any],
                              environment_state: Dict[str, Any],
                              current_round: int) -> str:
        """
        构建生成反馈的提示。
        
        Args:
            teacher_action: 教师行为信息
            student_performance: 学生表现信息
            student_state: 当前学生状态
            environment_state: 当前环境状态
            current_round: 当前交互轮数
            
        Returns:
            提示字符串
        """
        # 获取之前的反馈历史
        previous_feedback = ""
        if self.feedback_history:
            previous_feedback = "## 之前的反馈\n"
            for feedback in self.feedback_history[-3:]:  # 只包含最近3轮的反馈
                previous_feedback += f"轮次 {feedback['round']}:\n"
                previous_feedback += f"教师行为: {feedback['teacher_action'].get('action', '')}\n"
                previous_feedback += f"教师语言: {feedback['teacher_action'].get('speech', '')}\n"
                previous_feedback += f"反馈: {json.dumps(feedback['feedback'], ensure_ascii=False)}\n\n"
        
        prompt = f"""
        你是一位教育专家AI，需要根据教师的行为和语言以及学生的表现提供专业反馈。
        
        ## 教学内容
        {json.dumps(self.teaching_content, ensure_ascii=False, indent=2)}
        
        ## 评价标准
        {json.dumps(self.evaluation_criteria, ensure_ascii=False, indent=2)}
        
        ## 当前轮次
        {current_round}
        
        ## 教师行为
        行为: {teacher_action.get('action', '')}
        语言: {teacher_action.get('speech', '')}
        
        ## 学生表现
        行为: {student_performance.get('behavior', '')}
        语言: {student_performance.get('speech', '')}
        描述: {student_performance.get('description', '')}
        
        ## 学生状态
        {json.dumps(student_state, ensure_ascii=False, indent=2)}
        
        ## 环境状态
        {json.dumps(environment_state, ensure_ascii=False, indent=2)}
        
        {previous_feedback}
        
        请根据以上信息，特别是教师的行为和语言以及学生的表现，提供专业的教育反馈。
        反馈应包括：
        1. 教师行为的优点
        2. 教师行为的不足
        3. 改进建议
        4. 对学生状态的分析
        5. 对下一步教学的建议
        
        返回格式为JSON，包含以下字段：
        - strengths: 教师行为的优点（列表）
        - weaknesses: 教师行为的不足（列表）
        - suggestions: 改进建议（列表）
        - student_analysis: 对学生状态的分析（字符串）
        - next_steps: 对下一步教学的建议（字符串）
        - overall_comment: 总体评价（字符串）
        """
        
        return prompt
    
    def _build_final_report_prompt(self, interaction_history: List[Dict[str, Any]]) -> str:
        """
        构建生成最终报告的提示。
        
        Args:
            interaction_history: 交互历史记录
            
        Returns:
            提示字符串
        """
        # 提取交互历史的关键信息
        history_summary = ""
        for i, interaction in enumerate(interaction_history):
            history_summary += f"## 轮次 {i+1}\n"
            history_summary += f"教师行为: {interaction['teacher_action'].get('action', '')}\n"
            history_summary += f"教师语言: {interaction['teacher_action'].get('speech', '')}\n"
            history_summary += f"学生表现: {interaction['student_performance'].get('description', '')}\n"
            if 'expert_feedback' in interaction:
                history_summary += f"专家反馈: {json.dumps(interaction['expert_feedback'], ensure_ascii=False)}\n"
            history_summary += "\n"
        
        prompt = f"""
        你是一位教育专家AI，需要根据整个教学过程生成最终评估报告。
        
        ## 教学内容
        {json.dumps(self.teaching_content, ensure_ascii=False, indent=2)}
        
        ## 评价标准
        {json.dumps(self.evaluation_criteria, ensure_ascii=False, indent=2)}
        
        ## 交互历史摘要
        {history_summary}
        
        请根据以上信息，特别是整个教学过程中教师的表现和学生的反应，生成一份全面的评估报告。
        报告应包括：
        1. 教师表现的总体评价
        2. 教师的主要优点
        3. 教师的主要不足
        4. 具体的改进建议
        5. 对教师处理问题学生能力的评估
        6. 对未来教学的建议
        
        返回格式为JSON，包含以下字段：
        - overall_evaluation: 总体评价（字符串）
        - strengths: 主要优点（列表）
        - weaknesses: 主要不足（列表）
        - improvement_suggestions: 改进建议（列表）
        - problem_handling_assessment: 问题处理能力评估（字符串）
        - future_teaching_suggestions: 未来教学建议（字符串）
        - summary: 总结（字符串）
        """
        
        return prompt
    
    def _generate_feedback_with_llm(self, prompt: str) -> Dict[str, Any]:
        """
        使用LLM生成反馈。
        
        Args:
            prompt: 提示字符串
            
        Returns:
            包含反馈信息的字典
        """
        try:
            # 调用OpenAI API
            response = client.chat.completions.create(
                # model="gpt-4.1",
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "你是一位教育专家AI助手，根据提供的信息生成专业的教育反馈。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                # max_tokens=800,
                response_format={"type": "json_object"}
            )
            
            # 解析响应
            content = response.choices[0].message.content
            
            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content
            
            try:
                feedback = json.loads(json_str)
            except json.JSONDecodeError:
                # 如果无法解析JSON，则手动构建结果
                print(f"生成反馈时出错，无法解析JSON: {json_str}")
                feedback = {
                    "strengths": ["教师尝试与学生建立联系"],
                    "weaknesses": ["教师可能没有充分理解学生的问题"],
                    "suggestions": ["尝试更多地倾听学生的需求"],
                    "student_analysis": "学生似乎需要更多的关注和理解",
                    "next_steps": "建议教师尝试更多地了解学生的具体困难",
                    "overall_comment": "教师表现出了关心学生的态度，但需要更有针对性的教学策略"
                }
            
            return feedback
            
        except Exception as e:
            print(f"生成反馈时出错: {e}")
            # 返回默认反馈
            return {
                "strengths": ["教师尝试与学生建立联系"],
                "weaknesses": ["教师可能没有充分理解学生的问题"],
                "suggestions": ["尝试更多地倾听学生的需求"],
                "student_analysis": "学生似乎需要更多的关注和理解",
                "next_steps": "建议教师尝试更多地了解学生的具体困难",
                "overall_comment": "教师表现出了关心学生的态度，但需要更有针对性的教学策略"
            }
    
    def _generate_final_report_with_llm(self, prompt: str) -> Dict[str, Any]:
        """
        使用LLM生成最终报告。
        
        Args:
            prompt: 提示字符串
            
        Returns:
            包含最终报告的字典
        """
        try:
            # 调用OpenAI API
            response = client.chat.completions.create(
                # model="gpt-4.1",
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "你是一位教育专家AI助手，根据提供的信息生成全面的教学评估报告。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                # max_tokens=1000,
                response_format={"type": "json_object"}
            )
            
            # 解析响应
            content = response.choices[0].message.content
            
            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content
            
            try:
                report = json.loads(json_str)
            except json.JSONDecodeError:
                # 如果无法解析JSON，则手动构建结果
                print(f"生成最终报告时出错，无法解析JSON: {json_str}")
                report = {
                    "overall_evaluation": "教师在处理问题学生方面表现出了一定的能力，但仍有提升空间",
                    "strengths": ["教师能够保持耐心", "尝试多种方法与学生沟通"],
                    "weaknesses": ["对学生问题的根本原因理解不够深入", "教学策略不够灵活"],
                    "improvement_suggestions": ["加强对问题学生心理的理解", "学习更多针对性的教学技巧"],
                    "problem_handling_assessment": "教师能够处理基本的问题情况，但面对复杂问题时策略有限",
                    "future_teaching_suggestions": "建议参加专业培训，学习更多处理问题学生的方法",
                    "summary": "总体而言，教师展现了教育工作者应有的耐心和责任心，通过进一步学习和实践，有望成为更加优秀的教师"
                }
            
            return report
            
        except Exception as e:
            print(f"生成最终报告时出错: {e}")
            # 返回默认报告
            return {
                "overall_evaluation": "教师在处理问题学生方面表现出了一定的能力，但仍有提升空间",
                "strengths": ["教师能够保持耐心", "尝试多种方法与学生沟通"],
                "weaknesses": ["对学生问题的根本原因理解不够深入", "教学策略不够灵活"],
                "improvement_suggestions": ["加强对问题学生心理的理解", "学习更多针对性的教学技巧"],
                "problem_handling_assessment": "教师能够处理基本的问题情况，但面对复杂问题时策略有限",
                "future_teaching_suggestions": "建议参加专业培训，学习更多处理问题学生的方法",
                "summary": "总体而言，教师展现了教育工作者应有的耐心和责任心，通过进一步学习和实践，有望成为更加优秀的教师"
            }
