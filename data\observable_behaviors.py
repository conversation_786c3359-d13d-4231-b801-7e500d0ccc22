"""
学生可观察行为配置模块，定义学生哪些表现和行为是教师可以观察到的。
"""
from typing import Dict, Any, List


def get_default_observable_behaviors() -> Dict[str, Any]:
    """
    获取默认可观察行为配置。
    
    Returns:
        默认可观察行为配置字典
    """
    return {
        "all_observable": [
            # 身体语言
            {
                "category": "身体语言",
                "behaviors": [
                    "坐姿（挺直、弯腰、趴桌子等）",
                    "手部动作（做笔记、玩笔、敲桌子等）",
                    "腿部动作（抖腿、交叉等）",
                    "面部表情（微笑、皱眉、茫然等）",
                    "眼神接触（直视、回避、走神等）",
                    "头部姿势（点头、摇头、低头等）"
                ]
            },
            # 语言表达
            {
                "category": "语言表达",
                "behaviors": [
                    "回答问题的方式（积极、犹豫、拒绝等）",
                    "语速（快、慢、正常）",
                    "音量（大声、小声、正常）",
                    "语调（平稳、颤抖、兴奋等）",
                    "表达清晰度（清晰、模糊、结巴等）",
                    "使用的词汇（简单、复杂、专业等）"
                ]
            },
            # 学习行为
            {
                "category": "学习行为",
                "behaviors": [
                    "做笔记的情况（详细、简略、不做等）",
                    "提问情况（频繁、偶尔、从不等）",
                    "回答问题的准确性（准确、部分正确、错误等）",
                    "完成任务的速度（快、慢、中等）",
                    "参与讨论的程度（积极、被动、不参与等）",
                    "注意力集中程度（专注、分心、走神等）"
                ]
            },
            # 情绪表现
            {
                "category": "情绪表现",
                "behaviors": [
                    "情绪状态（平静、焦虑、兴奋等）",
                    "情绪变化（稳定、波动、突变等）",
                    "应对挫折的方式（坚持、放弃、寻求帮助等）",
                    "对成功的反应（高兴、平静、不在意等）",
                    "对批评的反应（接受、抵触、情绪低落等）",
                    "对表扬的反应（高兴、害羞、不在意等）"
                ]
            },
            # 社交互动
            {
                "category": "社交互动",
                "behaviors": [
                    "与教师的互动（积极、回避、对抗等）",
                    "与同学的互动（友好、孤立、冲突等）",
                    "小组活动中的角色（领导、跟随、旁观等）",
                    "寻求帮助的方式（主动、被动、拒绝等）",
                    "对他人需求的反应（关心、忽视、不耐烦等）",
                    "分享意愿（乐于分享、保留、拒绝等）"
                ]
            }
        ],
        "key_observable": [
            # 对于当前学生问题的关键可观察行为
            {
                "problem": "学习困难",
                "key_behaviors": [
                    "回避眼神接触",
                    "坐立不安",
                    "拒绝回答问题",
                    "情绪低落",
                    "做笔记时频繁擦除或修改",
                    "在遇到分数乘法问题时表现出明显的焦虑",
                    "对简单问题回答迅速，复杂问题则完全回避"
                ]
            },
            {
                "problem": "社交焦虑",
                "key_behaviors": [
                    "被点名时脸红",
                    "说话声音明显变小或颤抖",
                    "回避眼神接触",
                    "说话结巴",
                    "身体姿势变得僵硬",
                    "在小组活动中保持沉默",
                    "被要求发言时表现出明显的不适"
                ]
            }
        ],
        "interpretation_guide": {
            "attention": {
                "high": "眼神专注，积极回应，做笔记，提问相关问题",
                "medium": "间歇性注意，偶尔走神，需要提醒才能回到任务",
                "low": "频繁走神，玩弄物品，看向窗外，对提问反应迟钝"
            },
            "emotion": {
                "positive": "微笑，放松的姿势，积极参与，语调愉快",
                "neutral": "表情平静，姿势自然，参与度一般",
                "negative": "皱眉，紧张姿势，不愿参与，语调低沉或烦躁"
            },
            "engagement": {
                "high": "主动参与，举手提问，积极回答，与内容互动",
                "medium": "被动参与，回应提问，完成基本任务",
                "low": "不参与，拒绝回答，不完成任务，与内容无互动"
            },
            "understanding": {
                "high": "能准确回答问题，解释概念，应用知识解决问题",
                "medium": "部分理解概念，回答简单问题，需要帮助解决复杂问题",
                "low": "无法回答基本问题，概念混淆，无法应用知识"
            }
        }
    }


def get_observable_behaviors(behaviors_id: str = None) -> Dict[str, Any]:
    """
    根据ID获取可观察行为配置。
    
    Args:
        behaviors_id: 可观察行为配置ID，如果为None则返回默认配置
        
    Returns:
        可观察行为配置字典
    """
    # 这里可以根据ID从数据库或文件中加载不同的可观察行为配置
    # 目前仅实现返回默认配置
    return get_default_observable_behaviors()


def get_all_observable_behaviors() -> List[Dict[str, Any]]:
    """
    获取所有可用的可观察行为配置。
    
    Returns:
        可观察行为配置列表
    """
    # 这里可以从数据库或文件中加载所有可观察行为配置
    # 目前仅返回包含默认配置的列表
    return [get_default_observable_behaviors()]


def save_observable_behaviors(behaviors_id: str, behaviors_data: Dict[str, Any]) -> bool:
    """
    保存可观察行为配置。
    
    Args:
        behaviors_id: 可观察行为配置ID
        behaviors_data: 可观察行为配置数据
        
    Returns:
        保存是否成功
    """
    # 这里可以将配置保存到数据库或文件中
    # 目前仅打印信息
    print(f"保存可观察行为配置: {behaviors_id}")
    print(behaviors_data)
    return True
