"""
Web界面模块，实现基于Gradio的Web用户交互界面。
"""
from typing import Dict, Any, List, Tuple, Optional
import os
import json
import time
import gradio as gr

from core.simulation import Simulation
from data import teaching_content, student_profiles, observable_behaviors, evaluation_criteria


class WebUI:
    """
    Web界面类，实现基于Gradio的Web用户交互界面。
    """

    def __init__(self):
        """
        初始化Web界面。
        """
        self.simulation = None
        self.current_state = {}
        self.history = []
        self.is_completed = False

    def _initialize_simulation(self) -> Tuple[List[List[str]], str, str, str, str, str]:
        """
        初始化模拟系统。

        Returns:
            聊天历史、环境描述、学生状态、教学内容、当前轮次和专家反馈
        """
        # 加载配置
        teaching_content_data = teaching_content.get_default_teaching_content()
        student_profile_data = student_profiles.get_default_student_profile()
        observable_behaviors_data = observable_behaviors.get_default_observable_behaviors()
        evaluation_criteria_data = evaluation_criteria.get_default_evaluation_criteria()

        # 初始化模拟
        self.simulation = Simulation(
            student_profile=student_profile_data,
            teaching_content=teaching_content_data,
            observable_behaviors=observable_behaviors_data,
            evaluation_criteria=evaluation_criteria_data,
            max_rounds=10
        )

        # 开始模拟
        initial_state = self.simulation.start()
        self.current_state = initial_state
        self.is_completed = False
        self.history = []

        # 构建初始消息
        environment_description = initial_state.get("environment_description", "")
        student_performance = initial_state.get("student_performance", {})
        student_behavior = student_performance.get("behavior", "")
        student_speech = student_performance.get("speech", "")
        student_description = student_performance.get("description", "")

        system_message = f"**环境描述**：{environment_description}\n\n"
        system_message += f"**学生行为**：{student_behavior}\n\n"
        system_message += f"**学生语言**：\"{student_speech}\"\n\n"
        system_message += f"**整体描述**：{student_description}"

        # 更新历史
        self.history.append(["系统", system_message])

        # 返回初始状态（作为单独的返回值）
        return (
            self.history,
            environment_description,
            self._format_student_state(initial_state.get("student_state", {})),
            initial_state.get("teaching_content", ""),
            f"{initial_state.get('current_round', 0)}/{initial_state.get('max_rounds', 10)}",
            "尚无专家反馈"
        )

    def _process_teacher_input(self, teacher_input: str, history: List[List[str]]) -> Tuple[List[List[str]], str, str, str, str]:
        """
        处理教师输入。

        Args:
            teacher_input: 教师输入的行为和语言
            history: 聊天历史

        Returns:
            更新后的聊天历史、环境描述、学生状态、当前轮次和专家反馈
        """
        if self.is_completed:
            return history, self.current_state.get("environment_description", ""), \
                   self._format_student_state(self.current_state.get("student_state", {})), \
                   f"{self.current_state.get('current_round', 0)}/{self.current_state.get('max_rounds', 10)}", \
                   "模拟已结束，请查看最终报告"

        # 更新历史
        history.append(["教师", teacher_input])

        # 处理教师输入
        new_state = self.simulation.process_teacher_input(teacher_input)
        self.current_state = new_state

        # 检查是否完成
        if new_state.get("is_completed", False):
            self.is_completed = True
            final_report = new_state.get("final_report", {})

            # 构建最终报告消息
            report_message = "## 教学过程最终评估报告\n\n"

            # 总体评价
            overall_evaluation = final_report.get("overall_evaluation", "")
            report_message += f"**总体评价**：{overall_evaluation}\n\n"

            # 主要优点
            strengths = final_report.get("strengths", [])
            report_message += "**主要优点**：\n"
            for strength in strengths:
                report_message += f"- {strength}\n"
            report_message += "\n"

            # 主要不足
            weaknesses = final_report.get("weaknesses", [])
            report_message += "**主要不足**：\n"
            for weakness in weaknesses:
                report_message += f"- {weakness}\n"
            report_message += "\n"

            # 改进建议
            suggestions = final_report.get("improvement_suggestions", [])
            report_message += "**改进建议**：\n"
            for suggestion in suggestions:
                report_message += f"- {suggestion}\n"
            report_message += "\n"

            # 问题处理能力评估
            problem_handling = final_report.get("problem_handling_assessment", "")
            report_message += f"**问题处理能力评估**：{problem_handling}\n\n"

            # 未来教学建议
            future_suggestions = final_report.get("future_teaching_suggestions", "")
            report_message += f"**未来教学建议**：{future_suggestions}\n\n"

            # 总结
            summary = final_report.get("summary", "")
            report_message += f"**总结**：{summary}"

            # 更新历史
            history.append(["系统", report_message])

            return history, new_state.get("environment_description", ""), \
                   self._format_student_state(new_state.get("student_state", {})), \
                   f"{new_state.get('current_round', 0)}/{new_state.get('max_rounds', 10)}", \
                   "模拟已结束，请查看最终报告"

        # 构建学生反馈消息
        student_performance = new_state.get("student_performance", {})
        student_behavior = student_performance.get("behavior", "")
        student_speech = student_performance.get("speech", "")
        student_description = student_performance.get("description", "")

        # student_message = f"**学生行为**：{student_behavior}\n\n"
        student_message += f"**学生语言**：\"{student_speech}\"\n\n"
        student_message += f"**学生行为描述**：{student_description}"

        # 更新历史
        history.append(["学生", student_message])

        # 构建专家反馈
        expert_feedback = new_state.get("expert_feedback", {})
        expert_message = ""

        if expert_feedback:
            strengths = expert_feedback.get("strengths", [])
            weaknesses = expert_feedback.get("weaknesses", [])
            suggestions = expert_feedback.get("suggestions", [])
            student_analysis = expert_feedback.get("student_analysis", "")
            next_steps = expert_feedback.get("next_steps", "")
            overall_comment = expert_feedback.get("overall_comment", "")

            expert_message += "**优点**：\n"
            for strength in strengths:
                expert_message += f"- {strength}\n"
            expert_message += "\n"

            expert_message += "**不足**：\n"
            for weakness in weaknesses:
                expert_message += f"- {weakness}\n"
            expert_message += "\n"

            expert_message += "**建议**：\n"
            for suggestion in suggestions:
                expert_message += f"- {suggestion}\n"
            expert_message += "\n"

            expert_message += f"**学生分析**：{student_analysis}\n\n"
            expert_message += f"**下一步**：{next_steps}\n\n"
            expert_message += f"**总体评价**：{overall_comment}"
        else:
            expert_message = "尚无专家反馈"

        return history, new_state.get("environment_description", ""), \
               self._format_student_state(new_state.get("student_state", {})), \
               f"{new_state.get('current_round', 0)}/{new_state.get('max_rounds', 10)}", \
               expert_message

    def _format_student_state(self, student_state: Dict[str, Any]) -> str:
        """
        格式化学生状态信息。

        Args:
            student_state: 学生状态字典

        Returns:
            格式化后的学生状态字符串
        """
        state_text = ""
        for key, value in student_state.items():
            if isinstance(value, (int, float)) and key not in ["timestamp"]:
                state_text += f"**{key}**: {value}\n"

        if "notes" in student_state:
            state_text += f"\n**备注**: {student_state['notes']}"

        return state_text

    def _reset_simulation(self) -> Tuple[List[List[str]], str, str, str, str, str]:
        """
        重置模拟系统。

        Returns:
            聊天历史、环境描述、学生状态、教学内容、当前轮次和专家反馈
        """
        return self._initialize_simulation()

    def create_ui(self) -> gr.Blocks:
        """
        创建Gradio界面。

        Returns:
            Gradio Blocks界面
        """
        with gr.Blocks(title="教师-学生交互模拟系统") as demo:
            gr.Markdown("# 教师-学生交互模拟系统")
            gr.Markdown("本系统旨在帮助教师训练处理问题学生的能力。您将作为教师与模拟的学生互动，系统会根据您的行为和语言生成学生的反应，并提供专业的教育反馈。")

            with gr.Row():
                with gr.Column(scale=2):
                    chatbot = gr.Chatbot(
                        label="交互历史",
                        height=500,
                        avatar_images=("assets/sy.png", "assets/st.png")
                    )

                    with gr.Row():
                        teacher_input = gr.Textbox(
                            label="教师行为和语言",
                            placeholder="输入您的行为和语言，例如：(走到学生旁边) 你能告诉我你遇到了什么困难吗？",
                            lines=3
                        )

                    with gr.Row():
                        submit_btn = gr.Button("提交", variant="primary")
                        reset_btn = gr.Button("重置模拟")

                with gr.Column(scale=1):
                    with gr.Accordion("教学内容", open=True):
                        teaching_content_text = gr.Textbox(
                            label="当前教学内容",
                            lines=3,
                            interactive=False
                        )

                    with gr.Accordion("环境描述", open=True):
                        environment_description_text = gr.Textbox(
                            label="当前环境",
                            lines=3,
                            interactive=False
                        )

                    with gr.Accordion("学生状态（仅用于调试）", open=True):
                        student_state_text = gr.Markdown(
                            label="当前学生状态"
                        )

                    with gr.Accordion("当前进度", open=True):
                        current_round_text = gr.Textbox(
                            label="当前轮次",
                            interactive=False
                        )

                    with gr.Accordion("专家反馈", open=True):
                        expert_feedback_text = gr.Markdown(
                            label="专家反馈"
                        )

            # 初始化模拟
            demo.load(
                fn=self._initialize_simulation,
                outputs=[chatbot, environment_description_text, student_state_text,
                         teaching_content_text, current_round_text, expert_feedback_text]
            )

            # 提交按钮事件
            submit_btn.click(
                fn=self._process_teacher_input,
                inputs=[teacher_input, chatbot],
                outputs=[chatbot, environment_description_text, student_state_text,
                         current_round_text, expert_feedback_text],
                api_name="submit"
            ).then(
                fn=lambda: "",
                outputs=teacher_input
            )

            # 重置按钮事件
            reset_btn.click(
                fn=self._reset_simulation,
                outputs=[chatbot, environment_description_text, student_state_text,
                         teaching_content_text, current_round_text, expert_feedback_text],
                api_name="reset"
            )

            # 回车键提交
            teacher_input.submit(
                fn=self._process_teacher_input,
                inputs=[teacher_input, chatbot],
                outputs=[chatbot, environment_description_text, student_state_text,
                         current_round_text, expert_feedback_text],
            ).then(
                fn=lambda: "",
                outputs=teacher_input
            )

        return demo


def run(share: bool = False):
    """
    运行Web界面。

    Args:
        share: 是否公开分享（使用Gradio提供的公共URL）
    """
    ui = WebUI()
    demo = ui.create_ui()
    demo.launch(share=share)
