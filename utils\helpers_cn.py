"""
TutorHelper的辅助函数。
"""
import os
import json
from typing import Dict, List, Any, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    加载JSON文件。
    
    参数:
        file_path: JSON文件路径
        
    返回:
        包含JSON数据的字典
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"文件未找到: {file_path}")
        return {}
    except json.JSONDecodeError:
        logger.error(f"文件中的JSON无效: {file_path}")
        return {}

def save_json_file(file_path: str, data: Dict[str, Any]) -> bool:
    """
    将数据保存到JSON文件。
    
    参数:
        file_path: 保存JSON文件的路径
        data: 要保存的数据
        
    返回:
        成功返回True，否则返回False
    """
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存JSON文件时出错: {e}")
        return False

def extract_action_and_speech(teacher_input: str) -> tuple:
    """
    从输入中提取教师的动作和言语。
    假设动作在括号中，言语在括号外。
    
    参数:
        teacher_input: 教师的原始输入
        
    返回:
        (动作, 言语)的元组
    """
    import re
    
    # 提取动作（括号中的文本）
    actions = re.findall(r'\((.*?)\)', teacher_input)
    
    # 从输入中移除动作以获取言语
    speech = re.sub(r'\((.*?)\)', '', teacher_input).strip()
    
    return actions, speech

def format_interaction_history(history: List[Dict[str, Any]]) -> str:
    """
    格式化交互历史以便显示或记录。
    
    参数:
        history: 交互字典列表
        
    返回:
        历史记录的格式化字符串表示
    """
    formatted = []
    for i, interaction in enumerate(history):
        formatted.append(f"--- 轮次 {i+1} ---")
        formatted.append(f"环境: {interaction.get('environment', 'N/A')}")
        formatted.append(f"学生: {interaction.get('student_performance', 'N/A')}")
        formatted.append(f"教师: {interaction.get('teacher_input', 'N/A')}")
        formatted.append("")
    
    return "\n".join(formatted)

def validate_config(config: Dict[str, Any], required_keys: List[str]) -> bool:
    """
    验证配置字典是否包含所有必需的键。
    
    参数:
        config: 要验证的配置字典
        required_keys: 必需键的列表
        
    返回:
        有效返回True，否则返回False
    """
    missing_keys = [key for key in required_keys if key not in config]
    if missing_keys:
        logger.error(f"缺少必需的配置键: {missing_keys}")
        return False
    return True
