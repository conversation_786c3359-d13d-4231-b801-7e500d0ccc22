"""
模拟系统核心逻辑模块，包括交互流程和状态更新。
"""
from typing import Dict, List, Any, Optional, Tuple, Callable
import time

from core.state_manager import StateManager
from agents.student_agent import StudentAgent
from agents.expert_agent import ExpertAgent


class Simulation:
    """
    模拟系统类，负责协调学生代理、教育专家代理和状态管理器之间的交互。
    """

    def __init__(self,
                 student_profile: Dict[str, Any],
                 teaching_content: Dict[str, Any],
                 observable_behaviors: Dict[str, Any],
                 evaluation_criteria: Dict[str, Any],
                 environment_config: Optional[Dict[str, Any]] = None,
                 max_rounds: int = 10,
                 callback: Optional[Callable] = None):
        """
        初始化模拟系统。

        Args:
            student_profile: 学生基础信息和初始状态
            teaching_content: 教学内容配置
            observable_behaviors: 学生可观察行为配置
            evaluation_criteria: 教师评价标准配置
            environment_config: 环境配置信息（可选）
            max_rounds: 最大交互轮数
            callback: 每轮交互后的回调函数（可选）
        """
        self.state_manager = StateManager(student_profile, environment_config)
        self.student_agent = StudentAgent(student_profile, observable_behaviors)
        self.expert_agent = ExpertAgent(evaluation_criteria, teaching_content)

        self.teaching_content = teaching_content
        self.max_rounds = max_rounds
        self.callback = callback
        self.is_completed = False
        self.final_report = None

    def start(self) -> Dict[str, Any]:
        """
        开始模拟。

        Returns:
            初始状态信息
        """
        initial_state = self.state_manager.get_current_state()
        initial_performance = self.student_agent.generate_initial_performance(
            initial_state["student_state"],
            initial_state["environment_state"]
        )

        return {
            "environment_description": initial_state["environment_state"]["description"],
            "student_performance": initial_performance,
            "teaching_content": self.teaching_content["description"],
            "current_round": 0,
            "max_rounds": self.max_rounds
        }

    def process_teacher_input(self, teacher_input: str) -> Dict[str, Any]:
        """
        处理教师输入，推进模拟过程。

        Args:
            teacher_input: 教师输入的行为和语言

        Returns:
            包含新的学生表现、专家反馈等信息的字典
        """
        if self.is_completed:
            return {"error": "模拟已结束，无法继续交互"}

        current_state = self.state_manager.get_current_state()
        current_round = current_state["current_round"]

        if current_round >= self.max_rounds:
            self.is_completed = True
            self.final_report = self.generate_final_report()
            return {
                "is_completed": True,
                "final_report": self.final_report
            }

        # 解析教师输入，提取行为和语言
        teacher_action = self._parse_teacher_input(teacher_input)

        # 学生代理根据教师行为生成新的表现
        student_performance = self.student_agent.generate_performance(
            teacher_action,
            current_state["student_state"],
            current_state["environment_state"]
        )

        # 检查是否有环境变化，如果有则更新环境状态
        if student_performance.get("environment_change"):
            new_environment = {
                "description": student_performance["environment_change"]
            }
            self.state_manager.update_environment_state(new_environment)

        # 更新学生状态
        new_student_state = self.student_agent.update_state(
            teacher_action,
            current_state["student_state"]
        )
        self.state_manager.student_state = new_student_state

        # 教育专家代理生成反馈
        expert_feedback = self.expert_agent.generate_feedback(
            teacher_action,
            student_performance,
            new_student_state,
            current_state["environment_state"],
            current_round + 1
        )

        # 记录交互
        self.state_manager.record_interaction(
            teacher_action,
            student_performance,
            expert_feedback
        )

        # 检查是否达到结束条件
        if self._check_completion(new_student_state) or current_round + 1 >= self.max_rounds:
            self.is_completed = True
            self.final_report = self.generate_final_report()

        # 调用回调函数（如果有）
        if self.callback:
            self.callback(self.state_manager.get_current_state())

        # 获取最新的环境状态
        updated_state = self.state_manager.get_current_state()

        return {
            "environment_description": updated_state["environment_state"]["description"],
            "student_performance": student_performance,
            "expert_feedback": expert_feedback,
            "student_state": new_student_state,  # 仅用于调试，实际界面中不应显示
            "current_round": current_round + 1,
            "max_rounds": self.max_rounds,
            "is_completed": self.is_completed,
            "final_report": self.final_report if self.is_completed else None
        }

    def _parse_teacher_input(self, teacher_input: str) -> Dict[str, Any]:
        """
        解析教师输入，提取行为和语言。

        Args:
            teacher_input: 教师输入的行为和语言

        Returns:
            包含行为和语言的字典
        """
        # 简单实现：假设括号内为行为，括号外为语言
        import re

        actions = re.findall(r'\((.*?)\)', teacher_input)
        action_text = ' '.join(actions) if actions else ""

        # 移除括号内容，剩余部分为语言
        speech = re.sub(r'\(.*?\)', '', teacher_input).strip()

        return {
            "action": action_text,
            "speech": speech,
            "raw_input": teacher_input,
            "timestamp": time.time()
        }

    def _check_completion(self, student_state: Dict[str, Any]) -> bool:
        """
        检查模拟是否应该结束。

        Args:
            student_state: 当前学生状态

        Returns:
            如果应该结束，返回True；否则返回False
        """
        # 简单实现：检查学生状态中是否有"problem_solved"标志
        return student_state.get("problem_solved", False)

    def generate_final_report(self) -> Dict[str, Any]:
        """
        生成最终报告。

        Returns:
            包含教师表现评估的最终报告
        """
        interaction_history = self.state_manager.get_interaction_history()
        return self.expert_agent.generate_final_report(interaction_history)
